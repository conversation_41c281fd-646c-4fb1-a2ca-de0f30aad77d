import { NextResponse } from "next/server";
import { getAdminStats } from "@/lib/actions/admin";

export async function GET() {
  try {
    const result = await getAdminStats();
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error("Error in stats API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
