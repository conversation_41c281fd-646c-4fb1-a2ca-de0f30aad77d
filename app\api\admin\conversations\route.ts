import { NextResponse } from "next/server";
import { getAllConversations } from "@/lib/actions/admin";

export async function GET() {
  try {
    const result = await getAllConversations();
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error("Error in conversations API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
