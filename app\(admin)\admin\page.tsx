"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

export default function AdminPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isStripeLoading, setIsStripeLoading] = useState(false);
  const [isFixLoading, setIsFixLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [stripeResult, setStripeResult] = useState<any>(null);
  const [fixResult, setFixResult] = useState<any>(null);

  const seedPlans = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch("/api/admin/seed-plans", {
        method: "POST",
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: "Failed to seed plans", details: error });
    } finally {
      setIsLoading(false);
    }
  };

  const setupStripe = async () => {
    setIsStripeLoading(true);
    setStripeResult(null);

    try {
      const response = await fetch("/api/admin/setup-stripe-products", {
        method: "POST",
      });

      const data = await response.json();
      setStripeResult(data);
    } catch (error) {
      setStripeResult({ error: "Failed to setup Stripe", details: error });
    } finally {
      setIsStripeLoading(false);
    }
  };

  const fixStripePrices = async () => {
    setIsFixLoading(true);
    setFixResult(null);

    try {
      const response = await fetch("/api/admin/fix-stripe-prices", {
        method: "POST",
      });

      const data = await response.json();
      setFixResult(data);
    } catch (error) {
      setFixResult({ error: "Failed to fix Stripe prices", details: error });
    } finally {
      setIsFixLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
          Admin Panel
        </h1>

        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Database Management
          </h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">
                Seed Plans
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create the default subscription plans in the database.
              </p>

              <Button
                onClick={seedPlans}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white">
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creating Plans...
                  </>
                ) : (
                  "Create Plans"
                )}
              </Button>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">
                Setup Stripe Integration
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create Stripe products and prices, then update database with
                real price IDs.
              </p>

              <Button
                onClick={setupStripe}
                disabled={isStripeLoading}
                className="bg-green-600 hover:bg-green-700 text-white">
                {isStripeLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Setting up Stripe...
                  </>
                ) : (
                  "Setup Stripe Products"
                )}
              </Button>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">
                Fix Stripe Price IDs
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Fix invalid Stripe price IDs by creating new products and
                prices.
              </p>

              <Button
                onClick={fixStripePrices}
                disabled={isFixLoading}
                className="bg-red-600 hover:bg-red-700 text-white">
                {isFixLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Fixing Prices...
                  </>
                ) : (
                  "Fix Stripe Prices"
                )}
              </Button>
            </div>
          </div>
        </div>

        {fixResult && (
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Fix Prices Result
            </h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(fixResult, null, 2)}
            </pre>
          </div>
        )}

        {stripeResult && (
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Stripe Setup Result
            </h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(stripeResult, null, 2)}
            </pre>
          </div>
        )}

        {result && (
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Result
            </h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
