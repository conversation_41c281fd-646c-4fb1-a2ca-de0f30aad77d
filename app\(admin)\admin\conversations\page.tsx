"use client";

import { useState, useEffect } from "react";
import {
  MessageSquare,
  User,
  Calendar,
  Image,
  Eye,
  EyeOff,
} from "lucide-react";

interface Conversation {
  id: string;
  userId: string;
  userMessage: string;
  imageData: string | null;
  imageType: string | null;
  aiResponse: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    email: string;
    name: string | null;
    username: string | null;
    image: string | null;
  };
}

export default function ConversationsPage() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedConversations, setExpandedConversations] = useState<
    Set<string>
  >(new Set());

  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/conversations");

      if (!response.ok) {
        throw new Error("Failed to fetch conversations");
      }

      const data = await response.json();
      setConversations(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = (conversationId: string) => {
    const newExpanded = new Set(expandedConversations);
    if (newExpanded.has(conversationId)) {
      newExpanded.delete(conversationId);
    } else {
      newExpanded.add(conversationId);
    }
    setExpandedConversations(newExpanded);
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const conversationsWithImages = conversations.filter(
    (conv) => conv.imageData,
  ).length;
  const totalConversations = conversations.length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
        <p className="text-red-800 dark:text-red-200">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <MessageSquare className="h-8 w-8 text-purple-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Conversations
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              View all user conversations and coffee cup readings
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Conversations
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {totalConversations}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                With Images
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {conversationsWithImages}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MessageSquare className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Text Only
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {totalConversations - conversationsWithImages}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Conversations List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Recent Conversations
          </h3>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {conversations.map((conversation) => {
            const isExpanded = expandedConversations.has(conversation.id);

            return (
              <div
                key={conversation.id}
                className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-start space-x-4">
                  {/* User Avatar */}
                  <div className="flex-shrink-0">
                    {conversation.user.image ? (
                      <img
                        className="h-10 w-10 rounded-full"
                        src={conversation.user.image}
                        alt={conversation.user.name || conversation.user.email}
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Conversation Content */}
                  <div className="flex-1 min-w-0">
                    {/* User Info */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {conversation.user.name ||
                            conversation.user.username ||
                            "Anonymous"}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {conversation.user.email}
                        </p>
                        {conversation.imageData && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <Image className="h-3 w-3 mr-1" />
                            Image
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(
                            conversation.createdAt,
                          ).toLocaleDateString()}
                        </div>
                        <button
                          onClick={() => toggleExpanded(conversation.id)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          {isExpanded ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>

                    {/* User Message */}
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium">User:</span>{" "}
                        {isExpanded
                          ? conversation.userMessage
                          : truncateText(conversation.userMessage)}
                      </p>
                    </div>

                    {/* AI Response */}
                    <div className="mt-2">
                      <p className="text-sm text-gray-800 dark:text-gray-200">
                        <span className="font-medium text-blue-600 dark:text-blue-400">
                          AI:
                        </span>{" "}
                        {isExpanded
                          ? conversation.aiResponse
                          : truncateText(conversation.aiResponse, 150)}
                      </p>
                    </div>

                    {/* Image Preview (if expanded and has image) */}
                    {isExpanded && conversation.imageData && (
                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Uploaded Image:
                        </p>
                        <div className="relative">
                          <img
                            src={conversation.imageData}
                            alt="Coffee cup"
                            className="max-w-xs rounded-lg shadow-md"
                            onError={(e) => {
                              console.error("Image failed to load:", {
                                imageType: conversation.imageType,
                                imageDataLength: conversation.imageData?.length,
                                imageDataStart:
                                  conversation.imageData?.substring(0, 50),
                              });
                              e.currentTarget.style.display = "none";
                              const nextElement = e.currentTarget
                                .nextElementSibling as HTMLElement;
                              if (nextElement) {
                                nextElement.style.display = "block";
                              }
                            }}
                          />
                          <div className="hidden bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4 max-w-xs">
                            <p className="text-red-800 dark:text-red-200 text-sm">
                              Failed to load image. Check console for details.
                            </p>
                            <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                              Type: {conversation.imageType || "unknown"} |
                              Size: {conversation.imageData?.length || 0} chars
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
