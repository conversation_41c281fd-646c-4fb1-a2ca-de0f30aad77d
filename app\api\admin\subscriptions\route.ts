import { NextResponse } from "next/server";
import { getAllSubscriptions } from "@/lib/actions/admin";

export async function GET() {
  try {
    const result = await getAllSubscriptions();
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error("Error in subscriptions API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
