import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { analyzeCoffeeImage } from "@/lib/openai";
import { saveConversation } from "@/actions/save-conversation";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { message, image } = await request.json();

    if (!message || typeof message !== "string") {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 },
      );
    }

    // Get user's current readings data
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        id: true,
        readingsUsed: true,
        readingsLimit: true,
        subscriptionStatus: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has remaining readings
    const remainingReadings = user.readingsLimit - user.readingsUsed;

    if (remainingReadings <= 0) {
      return NextResponse.json(
        {
          error: "No readings remaining",
          message:
            "لقد استنفدت عدد القراءات المتاحة. يرجى ترقية باقتك للمتابعة.",
          remainingReadings: 0,
        },
        { status: 403 },
      );
    }

    // Increment readings used
    const updatedUser = await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        readingsUsed: user.readingsUsed + 1,
      },
      select: {
        readingsUsed: true,
        readingsLimit: true,
      },
    });

    // Generate AI response
    let aiResponse: string;

    try {
      if (image && image.data) {
        console.log("Processing image analysis request...");
        console.log("Image data type:", typeof image.data);
        console.log("Image data preview:", image.data.substring(0, 50) + "...");

        // Analyze coffee cup image using OpenAI Vision
        aiResponse = await analyzeCoffeeImage(image.data, message);
        console.log("AI response generated successfully");
      } else {
        console.log("No image provided, sending text-only response");
        // Handle text-only messages (for coffee cup reading only)
        aiResponse =
          "أهلاً حبيبي! 🔮✨ أنا مرجانة وأنا هنا عشان أقرأ لك فنجانك. عشان أقدر أشوف أسرار فنجانك وأحكي لك حكايته، لازم ترفع صورة واضحة للفنجان بعد ما تخلص شربه ☕💫 استنى شوفك! 🌟";
      }
    } catch (error) {
      console.error("Error generating AI response:", error);

      // More specific error handling
      if (error instanceof Error) {
        console.error("Error details:", {
          message: error.message,
          stack: error.stack,
        });

        // Return the specific error message if it's in Arabic (from our OpenAI function)
        if (error.message.includes("مشكلة") || error.message.includes("فشل")) {
          aiResponse = error.message;
        } else {
          aiResponse =
            "عذراً، حدث خطأ أثناء تحليل طلبك. يرجى المحاولة مرة أخرى.";
        }
      } else {
        aiResponse = "عذراً، حدث خطأ أثناء تحليل طلبك. يرجى المحاولة مرة أخرى.";
      }
    }

    // Save the conversation to database
    try {
      await saveConversation({
        userId: session.user.id,
        userMessage: message,
        imageData: image?.data,
        imageType: image?.type,
        aiResponse,
      });
      console.log("Conversation saved successfully");
    } catch (saveError) {
      console.error("Error saving conversation:", saveError);
      // Don't fail the request if saving conversation fails
    }

    const newRemainingReadings =
      updatedUser.readingsLimit - updatedUser.readingsUsed;

    return NextResponse.json({
      success: true,
      response: aiResponse,
      readingsUsed: updatedUser.readingsUsed,
      readingsLimit: updatedUser.readingsLimit,
      remainingReadings: Math.max(0, newRemainingReadings),
    });
  } catch (error) {
    console.error("Error processing chat message:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
