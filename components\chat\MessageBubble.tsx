import { IconUser, IconVolume, IconPlayerStop } from "@tabler/icons-react";
import { Message } from "@/types/dashboard";
import Image from "next/image";
import { useState, useRef } from "react";

interface MessageBubbleProps {
  message: Message;
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handlePlayAudio = async () => {
    if (isPlaying) {
      // Stop current audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      return;
    }

    try {
      setIsLoading(true);

      // Generate speech from OpenAI TTS
      const response = await fetch("/api/tts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text: message.content }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate speech");
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        audioRef.current.onended = () => {
          setIsPlaying(false);
          URL.revokeObjectURL(audioUrl);
        };
        audioRef.current.onerror = () => {
          setIsPlaying(false);
          setIsLoading(false);
          URL.revokeObjectURL(audioUrl);
        };

        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error("Error playing audio:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={`flex gap-2 sm:gap-3 lg:gap-4 ${
        message.sender === "user" ? "justify-end" : "justify-start"
      }`}>
      {message.sender === "assistant" && (
        <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden flex-shrink-0">
          <Image
            src="/morjana.jpg"
            alt="Morjana"
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <div
        className={`${
          message.sender === "user"
            ? "max-w-[85%] sm:max-w-[75%] lg:max-w-[70%]"
            : "max-w-[95%] sm:max-w-[90%] lg:max-w-[85%]"
        } rounded-2xl px-3 py-2 sm:px-4 sm:py-3 ${
          message.sender === "user"
            ? "bg-amber-600 text-white"
            : "bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600 shadow-sm"
        }`}>
        {/* Display image if present */}
        {message.image && (
          <div className="mb-2 sm:mb-3">
            <img
              src={message.image}
              alt="Uploaded image"
              className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600"
              style={{ maxHeight: "250px" }}
            />
          </div>
        )}

        {/* Display text content */}
        {message.content && (
          <div className="space-y-3">
            {message.sender === "assistant" ? (
              <div className="space-y-4">
                {/* Main Title with Voice Button */}
                <div className="flex items-center justify-between border-b-2 border-amber-200 dark:border-amber-600 pb-2 mb-4">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-amber-600 dark:text-amber-400">
                    🔮 قراءة الفنجان ✨
                  </h2>

                  {/* Voice Button */}
                  <button
                    onClick={handlePlayAudio}
                    disabled={isLoading}
                    className="flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-2 bg-amber-100 dark:bg-amber-900 hover:bg-amber-200 dark:hover:bg-amber-800 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title={isPlaying ? "إيقاف الصوت" : "تشغيل الصوت"}>
                    {isLoading ? (
                      <div className="w-4 h-4 border-2 border-amber-600 border-t-transparent rounded-full animate-spin"></div>
                    ) : isPlaying ? (
                      <IconPlayerStop className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                    ) : (
                      <IconVolume className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                    )}
                    <span className="text-xs text-amber-600 dark:text-amber-400 hidden sm:inline">
                      {isPlaying ? "إيقاف" : "استمع"}
                    </span>
                  </button>
                </div>

                {/* Formatted Content */}
                <div className="prose prose-sm sm:prose-base max-w-none">
                  <div className="text-xs sm:text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                    {message.content}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-xs sm:text-sm leading-relaxed whitespace-pre-wrap">
                {message.content}
              </p>
            )}
          </div>
        )}

        <p
          className={`text-xs mt-1 sm:mt-2 ${
            message.sender === "user"
              ? "text-amber-100"
              : "text-gray-500 dark:text-gray-400"
          }`}>
          {message.timestamp.toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </p>
      </div>

      {message.sender === "user" && (
        <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <IconUser className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
        </div>
      )}

      {/* Hidden audio element for TTS */}
      <audio ref={audioRef} style={{ display: "none" }} />
    </div>
  );
}
