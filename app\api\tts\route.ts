import { NextRequest, NextResponse } from "next/server";
import { openai } from "@/lib/openai";
import { auth } from "@/auth";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { text } = await request.json();

    if (!text) {
      return NextResponse.json({ error: "Text is required" }, { status: 400 });
    }

    // Clean the text from emojis and special characters for better TTS
    const cleanText = text
      .replace(/[🔮✨💫☕💕❤️🌹💖🌟]/g, "") // Remove emojis
      .replace(/\s+/g, " ") // Replace multiple spaces with single space
      .trim();

    console.log(
      "Generating speech for text:",
      cleanText.substring(0, 100) + "...",
    );

    // Generate speech using OpenAI TTS
    const mp3 = await openai.audio.speech.create({
      model: "tts-1",
      voice: "nova", // Female voice that works well with Arabic
      input: cleanText,
      response_format: "mp3",
    });

    // Convert the response to a buffer
    const buffer = Buffer.from(await mp3.arrayBuffer());

    // Return the audio file
    return new NextResponse(buffer, {
      headers: {
        "Content-Type": "audio/mpeg",
        "Content-Length": buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error("TTS Error:", error);
    return NextResponse.json(
      { error: "Failed to generate speech" },
      { status: 500 },
    );
  }
}
