// Public routes that don't require authentication
export const publicRoutes = ["/", "/login", "/signup", "/plans"];

// Routes that require authentication
export const privateRoutes = ["/chat"];

// Routes that are only for authenticated users (redirect to chat if not logged in)
export const authRoutes = ["/login", "/signup"];

// Routes that are accessible to all authenticated users (CUSTOMER role)
export const customerRoutes = [
  "/chat",
  "/plans",
  "/test-plans",
  "/debug-plans",
  "/test-checkout",
  "/debug-checkout",
];

// Routes that are only accessible to ADMIN users
export const adminRoutes = ["/admin"];

// API routes that don't require authentication
export const publicApiRoutes = ["/api/auth", "/api/webhooks"];

// API routes that require ADMIN role
export const adminApiRoutes = ["/api/admin"];
