import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { createSubscription, updateSubscription } from "@/data/subscription";
import { getPlanById } from "@/data/plan";
import prisma from "@/lib/prisma";
import Stripe from "stripe";

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  if (!webhookSecret) {
    console.error("STRIPE_WEBHOOK_SECRET is not set");
    return NextResponse.json(
      { error: "Webhook secret not configured" },
      { status: 500 },
    );
  }

  const body = await request.text();
  const signature = request.headers.get("stripe-signature");

  if (!signature) {
    console.error("No Stripe signature found");
    return NextResponse.json({ error: "No signature found" }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (error) {
    console.error("Webhook signature verification failed:", error);
    return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
  }

  try {
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;
        await handleCheckoutSessionCompleted(session);
        break;
      }

      case "customer.subscription.created":
      case "customer.subscription.updated": {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionChange(subscription);
        break;
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(subscription);
        break;
      }

      case "invoice.payment_succeeded": {
        const invoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentSucceeded(invoice);
        break;
      }

      case "invoice.payment_failed": {
        const invoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentFailed(invoice);
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 },
    );
  }
}

async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session,
) {
  const userId = session.metadata?.userId;
  const planId = session.metadata?.planId;

  if (!userId || !planId) {
    console.error("Missing metadata in checkout session");
    return;
  }

  // Get the subscription from Stripe
  if (session.subscription && typeof session.subscription === "string") {
    const stripeSubscription = await stripe.subscriptions.retrieve(
      session.subscription,
    );
    await handleSubscriptionChange(stripeSubscription);
  }
}

async function handleSubscriptionChange(
  stripeSubscription: Stripe.Subscription,
) {
  const customerId = stripeSubscription.customer as string;

  // Find user by Stripe customer ID
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customerId },
  });

  if (!user) {
    console.error("User not found for customer:", customerId);
    return;
  }

  // Get the price ID from the subscription
  const priceId = stripeSubscription.items.data[0]?.price.id;
  if (!priceId) {
    console.error("No price ID found in subscription");
    return;
  }

  // Find the plan by Stripe price ID
  const plan = await prisma.plan.findFirst({
    where: { stripePriceId: priceId },
  });

  if (!plan) {
    console.error("Plan not found for price ID:", priceId);
    return;
  }

  // Validate and convert Stripe timestamps to dates
  const currentPeriodStart = (stripeSubscription as any).current_period_start
    ? new Date((stripeSubscription as any).current_period_start * 1000)
    : new Date();

  const currentPeriodEnd = (stripeSubscription as any).current_period_end
    ? new Date((stripeSubscription as any).current_period_end * 1000)
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // Default to 30 days from now

  const subscriptionData = {
    status: mapStripeStatusToLocal(stripeSubscription.status),
    currentPeriodStart,
    currentPeriodEnd,
    cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
  };

  // Check if subscription already exists
  const existingSubscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: stripeSubscription.id },
  });

  if (existingSubscription) {
    // Update existing subscription
    await updateSubscription(stripeSubscription.id, subscriptionData);
  } else {
    // Create new subscription
    await createSubscription({
      userId: user.id,
      stripeSubscriptionId: stripeSubscription.id,
      stripePriceId: priceId,
      ...subscriptionData,
    });
  }

  // Update user's plan and readings limit
  await prisma.user.update({
    where: { id: user.id },
    data: {
      currentPlanId: plan.id,
      readingsLimit: plan.readingsLimit,
      subscriptionStatus: subscriptionData.status,
    },
  });
}

async function handleSubscriptionDeleted(
  stripeSubscription: Stripe.Subscription,
) {
  await updateSubscription(stripeSubscription.id, {
    status: "CANCELED",
    cancelAtPeriodEnd: true,
  });

  // Optionally revert user to free plan
  const customerId = stripeSubscription.customer as string;
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customerId },
  });

  if (user) {
    // Find free plan
    const freePlan = await prisma.plan.findFirst({
      where: { price: 0, isActive: true },
    });

    if (freePlan) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          currentPlanId: freePlan.id,
          readingsLimit: freePlan.readingsLimit,
          subscriptionStatus: "TRIAL",
        },
      });
    }
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  if ((invoice as any).subscription) {
    const stripeSubscription = await stripe.subscriptions.retrieve(
      (invoice as any).subscription as string,
    );
    await handleSubscriptionChange(stripeSubscription);
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  if ((invoice as any).subscription) {
    await updateSubscription((invoice as any).subscription as string, {
      status: "PAST_DUE",
    });
  }
}

function mapStripeStatusToLocal(stripeStatus: Stripe.Subscription.Status) {
  switch (stripeStatus) {
    case "active":
      return "ACTIVE" as const;
    case "canceled":
      return "CANCELED" as const;
    case "past_due":
      return "PAST_DUE" as const;
    case "unpaid":
      return "UNPAID" as const;
    case "trialing":
      return "TRIAL" as const;
    default:
      return "TRIAL" as const;
  }
}
